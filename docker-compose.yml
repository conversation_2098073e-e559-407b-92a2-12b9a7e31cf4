services:
  frontend:
    container_name: canva-editor-frontend
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "4173:4173"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:4173"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - canva-network

networks:
  canva-network:
    driver: bridge
