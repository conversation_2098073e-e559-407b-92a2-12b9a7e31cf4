<svg xmlns="http://www.w3.org/2000/svg" width="1920" height="1080" viewBox="0 0 1920 1080" fill="none">
  <!-- Gradient background -->
  <rect width="1920" height="1080" fill="url(#paint0_linear)" />
  
  <!-- Abstract shapes -->
  <path d="M1920 0C1750 150 1600 50 1400 200C1200 350 1100 250 900 400C700 550 500 450 300 600C100 750 0 650 0 800V1080H1920V0Z" fill="url(#paint1_linear)" fill-opacity="0.4" />
  <path d="M0 1080C170 930 320 1030 520 880C720 730 820 830 1020 680C1220 530 1420 630 1620 480C1820 330 1920 430 1920 280V0H0V1080Z" fill="url(#paint2_linear)" fill-opacity="0.3" />
  
  <!-- Floating circles -->
  <circle cx="200" cy="200" r="80" fill="white" fill-opacity="0.05" />
  <circle cx="1700" cy="800" r="120" fill="white" fill-opacity="0.05" />
  <circle cx="500" cy="900" r="60" fill="white" fill-opacity="0.05" />
  <circle cx="1300" cy="300" r="100" fill="white" fill-opacity="0.05" />
  
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="1920" y2="1080" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4776E6" />
      <stop offset="1" stop-color="#8E54E9" />
    </linearGradient>
    <linearGradient id="paint1_linear" x1="0" y1="540" x2="1920" y2="540" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#8E54E9" />
      <stop offset="1" stop-color="#4776E6" />
    </linearGradient>
    <linearGradient id="paint2_linear" x1="1920" y1="540" x2="0" y2="540" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#8E54E9" />
      <stop offset="1" stop-color="#4776E6" />
    </linearGradient>
  </defs>
</svg>