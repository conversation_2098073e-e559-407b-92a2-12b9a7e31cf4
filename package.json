{"name": "adstudi", "private": true, "version": "0.0.1", "type": "module", "engines": {"node": ">=22.0.0", "npm": ">=10.0.0", "yarn": ">=1.22.0"}, "scripts": {"dev": "vite --host 0.0.0.0 --port 5173", "api": "cd api && yarn dev", "build": "tsc --skipLibCheck && vite build", "build-editor": "cd packages/editor & tsc --skipLibCheck --project tsconfig.json && vite build", "start": "serve -s dist -l 3000", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "npx vite preview --host 0.0.0.0 --port 4173"}, "main": "src/index.jsx", "module": "src/index.jsx", "dependencies": {"@emotion/react": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-tooltip": "^1.2.0", "@remotion/cli": "^4.0.295", "@remotion/core": "^1.0.0-y.46", "@remotion/player": "^4.0.295", "@shadcn/ui": "^0.0.4", "@tabler/icons-react": "^3.31.0", "@tailwindcss/postcss": "^4.1.3", "@tanstack/react-query": "^5.75.2", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "form-data": "^4.0.2", "framer-motion": "^12.7.3", "fuse.js": "^7.1.0", "gsap": "^3.12.2", "idb": "^8.0.2", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "lucide-react": "^0.487.0", "motion": "^12.6.5", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-hook-form": "^7.55.0", "react-phone-number-input": "^3.4.12", "react-router-dom": "^7.5.0", "sonner": "^2.0.3", "styled-components": "^6.0.8", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "url": "^0.11.3", "uuid": "^11.1.0", "vite-plugin-lib-inject-css": "^1.3.0", "vite-plugin-linter": "^2.0.7", "vite-tsconfig-paths": "^4.2.1", "zod": "^3.24.2"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20.9.2", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.21", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "i": "^0.3.7", "npm": "^11.3.0", "postcss": "^8.5.3", "serve": "^14.2.1", "tailwindcss": "^4.1.3", "tailwindcss-animate": "^1.0.7", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.2", "vite": "^5.0.0", "vite-plugin-dts": "^3.6.3"}, "workspaces": ["api", "packages/editor"]}