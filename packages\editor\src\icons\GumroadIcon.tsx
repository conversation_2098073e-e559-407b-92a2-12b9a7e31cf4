import { IconProps } from 'canva-editor/types';
import React from 'react';

const GumroadIcon: React.FC<IconProps> = ({ className = '' }: IconProps) => {
  return (
    <svg
      className={className}
      viewBox='0 0 500 500'
      height='36'
      width='36'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M278.037 414.107C356.994 414.107 421.001 352.319 421.001 276.099C421.001 199.879 356.994 138.09 278.037 138.09C199.08 138.09 135.072 199.879 135.072 276.099C135.072 352.319 199.08 414.107 278.037 414.107Z'
        fill='black'
      />
      <path
        d='M241.141 385.186C324.185 385.186 391.987 320.131 391.987 239.295C391.987 158.46 324.185 93.4038 241.141 93.4038C158.098 93.4038 90.295 158.46 90.295 239.295C90.295 320.131 158.098 385.186 241.141 385.186Z'
        fill='#FF90E8'
        stroke='black'
        strokeWidth='1.5625'
      />
      <path
        d='M229.795 312.898C187.578 312.898 162.745 278.788 162.745 236.358C162.745 192.263 190.061 156.489 242.21 156.489C296.016 156.489 314.226 193.096 315.054 213.894H276.149C275.322 202.247 265.388 184.776 241.383 184.776C215.723 184.776 199.167 207.239 199.167 234.694C199.167 262.149 215.723 284.611 241.383 284.611C264.561 284.611 274.494 266.308 278.633 248.006H241.383V233.03H319.545V309.57H285.255V261.316C282.771 278.788 272.01 312.898 229.795 312.898Z'
        fill='black'
      />
    </svg>
  );
};

export default GumroadIcon;
