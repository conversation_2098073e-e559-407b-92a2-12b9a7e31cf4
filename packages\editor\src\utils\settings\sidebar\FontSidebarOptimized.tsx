import React, { ForwardRefRenderFunction, useMemo, useState, useRef, useEffect } from "react";
import styled from "@emotion/styled";
import { FontData, FontDataApi } from "canva-editor/types";
import { useEditor } from "canva-editor/hooks";
import { useUsedFont } from "canva-editor/hooks/useUsedFont";
import { useVirtualizedFonts, usePopularFonts, useLazyFontLoad } from "@/hooks/useFonts";
import { groupFontsByFamily, handleFontStyle, handleFontStyleName } from "canva-editor/utils/fontHelper";
import { some } from "lodash";
import { FixedSizeList as List } from "react-window";
import InfiniteLoader from "react-window-infinite-loader";

// Icons (assuming these exist)
import ArrowRightIcon from "canva-editor/icons/ArrowRightIcon";
import  ArrowDownIcon  from "canva-editor/icons/ArrowDownIcon";
import CheckIcon from "canva-editor/icons/CheckIcon";
import  DocumentIcon  from "canva-editor/icons/DocumentIcon";

import  TrendingIcon  from "canva-editor/icons/TrendingIcon";
import  OutlineButton  from "canva-editor/components/button/OutlineButton";
import FontSearchBox from "../components/FontSearchBox";

import HorizontalCarousel from "canva-editor/components/carousel/HorizontalCarousel";
import Sidebar, { SidebarProps } from "./Sidebar";

export interface FontSidebarProps extends SidebarProps {
  selected: FontData[];
  onChangeFontFamily: (font: FontData) => void;
}

const Container = styled("div")`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
`;

const ListItem = styled("div")`
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 0 12px;
  :hover {
    background: #f9f9f9;
  }

  > span:nth-of-type(1) {
    flex: 0 1 auto;
    width: 24px;
    margin: 6px 8px 0 0;
    color: rgb(169 169 173);
  }

  > span:nth-of-type(2) {
    margin-right: auto;
    font-size: 16px;
  }
`;

const FontDisplay = styled("span")<{ fontStyle: string }>(
  ({ fontStyle }) => `
    text-transform: capitalize;
    ${handleFontStyle(fontStyle)};
`
);

const VirtualizedListContainer = styled("div")`
  flex: 1;
  min-height: 0;
`;

const LoadingItem = styled("div")`
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  color: #666;
  font-style: italic;
`;

// Flatten fonts from API format to component format
const flatFonts = (fonts: FontDataApi[]): FontData[] => {
  return fonts.reduce((acc: FontData[], font: FontDataApi) => {
    return acc.concat(
      font.styles.map((s) => ({
        family: font.family,
        name: s.name,
        url: s.url,
        style: s.style,
      }))
    );
  }, []);
};

interface VirtualizedFontItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    fonts: FontData[];
    selected: FontData[];
    onChangeFontFamily: (font: FontData) => void;
    openingItems: number[];
    setOpeningItems: React.Dispatch<React.SetStateAction<number[]>>;
    loadFontFamily: (family: string) => Promise<FontDataApi | null>;
  };
}

const VirtualizedFontItem: React.FC<VirtualizedFontItemProps> = ({ index, style, data }) => {
  const { fonts, selected, onChangeFontFamily, openingItems, setOpeningItems, loadFontFamily } = data;
  const font = fonts[index];


  if (!font) {
    return (
      <div style={style}>
        <LoadingItem>Loading...</LoadingItem>
      </div>
    );
  }

  // Auto-load font for preview when component mounts
  useEffect(() => {
    const loadFontForPreview = async () => {
      try {
        console.log("Loading font for preview:", font.family);
        await loadFontFamily(font.family);
        console.log("Font loaded successfully:", font.family);
      } catch (error) {
        console.warn("Failed to load font for preview:", font.family, error);
      }
    };

    loadFontForPreview();
  }, [font.family, loadFontFamily]);

  const handleToggleChildren = (openingItems: number[], index: number) => {
    const currentIndex = openingItems.indexOf(index);
    if (currentIndex === -1) {
      return [...openingItems, index];
    } else {
      return openingItems.filter((item) => item !== index);
    }
  };

  const handleFontClick = async (font: FontData) => {
    // Lazy load full font family data if needed
    if (font.styles && font.styles.length === 0) {
      const fullFontData = await loadFontFamily(font.family);
      if (fullFontData) {
        // Update font with full styles data
        font.styles = flatFonts([fullFontData]);
      }
    }
    onChangeFontFamily(font);
  };

  const isExpanded = openingItems.indexOf(index) > -1;

  return (
    <div style={style}>
      <ListItem onClick={() => handleFontClick(font)}>
        <span>
          {font.styles && font.styles?.length > 1 && (
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setOpeningItems(handleToggleChildren(openingItems, index));
              }}
              css={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                padding: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '20px',
                height: '20px',
                '&:hover': {
                  backgroundColor: '#f0f0f0',
                  borderRadius: '2px'
                }
              }}
            >
              {isExpanded ? (
                <ArrowDownIcon />
              ) : (
                <ArrowRightIcon />
              )}
            </button>
          )}
        </span>
        <FontDisplay
          css={{
            fontFamily: `'${font.family}', sans-serif`,
            transition: 'font-family 0.2s ease',
          }}
          fontStyle={font.style}
        >
          {!font.styles?.length ? font.name : font.family}
        </FontDisplay>
        <span>
          {!isExpanded &&
            some(font.styles, (fontStyle) =>
              selected.map((s) => s.name).includes(fontStyle.name)
            ) && <CheckIcon />}
        </span>
      </ListItem>
      {/* Render expanded styles */}
      {isExpanded &&
        font.styles &&
        font.styles?.length > 1 &&
        font.styles.map((fontStyle, subIdx) => (
          <ListItem
            css={{ marginLeft: 16,background }}
            key={subIdx + "-" + fontStyle.name}
            onClick={() => onChangeFontFamily(fontStyle)}
          >
            <span></span>
            <FontDisplay
              css={{
                fontFamily: `'${font.family}', sans-serif`,
                transition: 'font-family 0.2s ease',
              }}
              fontStyle={fontStyle.style}
            >
              {handleFontStyleName(fontStyle.style)}
            </FontDisplay>
            <span>
              {selected
                .map((s) => s.name)
                .includes(fontStyle.name) && <CheckIcon />}
            </span>
          </ListItem>
        ))}
    </div>
  );
};

const FontSidebarOptimized: ForwardRefRenderFunction<HTMLDivElement, FontSidebarProps> = (
  { selected, onChangeFontFamily, ...props },
  ref
) => {
  const { usedFonts } = useUsedFont();
  const { actions } = useEditor();
  const { loadFontFamily } = useLazyFontLoad();

  const [keyword, setKeyword] = useState("");
  const [openingItems, setOpeningItems] = useState<number[]>([]);

  // Load popular fonts first for quick preview
  const { data: popularFontsData, isLoading: popularLoading, error: popularError } = usePopularFonts(20);

  // Use virtualized loading for all fonts
  const {
    data: virtualizedData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: virtualizedLoading,
    error: virtualizedError
  } = useVirtualizedFonts(keyword, 50);

  // Debug logging
  console.log("FontSidebarOptimized render:", {
    popularFontsData,
    popularLoading,
    popularError,
    virtualizedData,
    virtualizedLoading,
    virtualizedError,
    selected,
    keyword
  });

  // Process fonts data
  const fontList = useMemo(() => {
    if (!virtualizedData?.pages) return [];
    
    const allFonts = virtualizedData.pages.flatMap(page => page.data);
    const flattened = flatFonts(allFonts);
    const grouped = groupFontsByFamily(flattened);
    
    return grouped;
  }, [virtualizedData]);

  // Popular fonts for quick access
  const popularFonts = useMemo(() => {
    if (!popularFontsData) return [];
    const flattened = flatFonts(popularFontsData);
    return groupFontsByFamily(flattened).slice(0, 10);
  }, [popularFontsData]);

  // Update editor font list
  useEffect(() => {
    if (fontList.length > 0) {
      actions.setFontList(fontList);
    }
  }, [fontList, actions]);

  // Auto-preload font previews for better UX
  useEffect(() => {
    const preloadGoogleFonts = () => {
      // Collect unique font families to preload
      const fontsToPreload = new Set<string>();

      // Add popular fonts
      popularFonts.slice(0, 8).forEach(font => {
        fontsToPreload.add(font.family);
      });

      // Add first batch of virtualized fonts
      fontList.slice(0, 12).forEach(font => {
        fontsToPreload.add(font.family);
      });

      if (fontsToPreload.size > 0) {
        console.log("Preloading font previews for:", Array.from(fontsToPreload));

        // Create Google Fonts URL for batch loading
        const fontFamilies = Array.from(fontsToPreload)
          .map(family => encodeURIComponent(family.replace(/\s+/g, '+')))
          .join('&family=');

        const googleFontsUrl = `https://fonts.googleapis.com/css2?family=${fontFamilies}&display=swap`;

        // Check if already loaded
        const existingLink = document.querySelector(`link[href*="fonts.googleapis.com"][href*="${fontFamilies.split('&')[0]}"]`);
        if (!existingLink) {
          const link = document.createElement('link');
          link.href = googleFontsUrl;
          link.rel = 'stylesheet';
          link.type = 'text/css';
          document.head.appendChild(link);

          // Optional: Remove old font links to prevent accumulation
          const oldLinks = document.querySelectorAll('link[href*="fonts.googleapis.com"]');
          if (oldLinks.length > 5) {
            oldLinks[0].remove();
          }
        }
      }
    };

    // Only preload when sidebar is open and we have fonts
    if (props.open && (popularFonts.length > 0 || fontList.length > 0)) {
      // Small delay to let the sidebar render first
      const timer = setTimeout(preloadGoogleFonts, 100);
      return () => clearTimeout(timer);
    }
  }, [popularFonts, fontList, props.open]);

  const handleSearch = (searchKeyword: string) => {
    setKeyword(searchKeyword);
  };

  const renderHeader = () => (
    <div css={{ padding: "16px 16px 0" }}>
      <FontSearchBox onSearch={handleSearch} />
      {/* Popular fonts carousel */}
      {popularFonts.length > 0 && !keyword && (
        <div css={{ marginTop: 8 }}>
          <HorizontalCarousel>
            {popularFonts.map((font, idx) => (
              <div key={`popular-${idx}`} className="carousel-item">
                <OutlineButton onClick={() => onChangeFontFamily(font)}>
                  <FontDisplay
                    css={{
                      fontFamily: `'${font.family}', sans-serif`,
                    }}
                    fontStyle={font.style}
                  >
                    {font.family}
                  </FontDisplay>
                </OutlineButton>
              </div>
            ))}
          </HorizontalCarousel>
        </div>
      )}
    </div>
  );

  // Check if item is loaded for infinite loader
  const isItemLoaded = (index: number) => !!fontList[index];

  // Load more items - InfiniteLoader expects (startIndex, stopIndex) => Promise<void>
  const loadMoreItems = async (_startIndex: number, _stopIndex: number): Promise<void> => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  const itemCount = hasNextPage ? fontList.length + 1 : fontList.length;

  return (
    <Sidebar {...props}>
      <Container ref={ref}>
        {renderHeader()}

        {/* Used fonts section */}
        {usedFonts.length > 0 && (
          <div>
            <div css={{ padding: "16px 16px 8px 8px", fontWeight: 700, display: "flex", columnGap: 8, alignItems: "center" }}>
              <DocumentIcon css={{ width: 24 }} />
              <span>Document fonts</span>
            </div>
            {usedFonts.slice(0, 5).map((font, idx) => (
              <ListItem key={`used-${idx}`} onClick={() => onChangeFontFamily(font)}>
                <span></span>
                <FontDisplay css={{ fontFamily: `'${font.family}', sans-serif` }} fontStyle={font.style}>
                  {font.family}
                </FontDisplay>
                <span>
                  {selected.map((s) => s.name).includes(font.name) && <CheckIcon />}
                </span>
              </ListItem>
            ))}
          </div>
        )}

        {/* All fonts section with virtualization */}
        <div css={{ padding: "16px 16px 8px 8px", fontWeight: 700, display: "flex", columnGap: 8, alignItems: "center" }}>
          <TrendingIcon />
          <span>{keyword ? 'Search Results' : 'All fonts'}</span>
        </div>

        <VirtualizedListContainer>
          {virtualizedLoading && fontList.length === 0 ? (
            <LoadingItem>Loading fonts...</LoadingItem>
          ) : (
            <InfiniteLoader
              isItemLoaded={isItemLoaded}
              itemCount={itemCount}
              loadMoreItems={loadMoreItems}
            >
              {({ onItemsRendered, ref: infiniteRef }) => (
                <List
                  ref={infiniteRef}
                  height={480}
                  width="100%"
                  itemCount={itemCount}
                  itemSize={40}
                  onItemsRendered={onItemsRendered}
                  itemData={{
                    fonts: fontList,
                    selected,
                    onChangeFontFamily,
                    openingItems,
                    setOpeningItems,
                    loadFontFamily,
                  }}
                >
                  {VirtualizedFontItem}
                </List>
              )}
            </InfiniteLoader>
          )}
        </VirtualizedListContainer>
      </Container>
    </Sidebar>
  );
};

export default React.forwardRef(FontSidebarOptimized);
