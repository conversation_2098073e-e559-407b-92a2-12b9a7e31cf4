import { IconProps } from 'canva-editor/types';
import React from 'react';

const ConfigurationIcon: React.FC<IconProps> = ({
  className = '',
}: IconProps) => {
  return (
    <svg
      className={className}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
    >
      <path
        fill='currentColor'
        d='M9 17.77c.37.19.75.34 1.15.46l.71 2.2c.73.1 1.47.1 2.2 0l.7-2.17c.47-.13.9-.31 1.31-.53l2.09 1.03c.58-.44 1.1-.96 1.55-1.53l-1.02-2.08c.25-.46.45-.95.6-1.47l2.16-.78c.08-.73.07-1.46-.05-2.18l-2.2-.7a6.47 6.47 0 0 0-.58-1.3l1-2.05c-.46-.57-1-1.09-1.59-1.52L15 6.23a6.46 6.46 0 0 0-1.15-.46l-.71-2.2c-.73-.1-1.47-.1-2.2 0l-.7 2.17c-.47.13-.9.31-1.31.53L6.84 5.24c-.58.44-1.1.96-1.55 1.53l1.02 2.08c-.25.46-.45.95-.6 1.47l-2.16.78c-.08.73-.07 1.46.05 2.18l2.2.7c.15.46.35.89.58 1.3l-1 2.05c.46.57 1 1.09 1.59 1.52L9 17.77zm-.02 1.71-2.1 1.11a9.94 9.94 0 0 1-3.24-3.1l1.1-2.27-2.44-.78a9.95 9.95 0 0 1-.1-4.45l2.55-.92-1.2-2.43a9.95 9.95 0 0 1 3.17-3.13l2.3 1.13.77-2.4a9.94 9.94 0 0 1 4.5.02l.73 2.26 2.1-1.11a9.94 9.94 0 0 1 3.24 3.1l-1.1 2.27 2.44.78c.36 1.43.4 2.95.1 4.45l-2.55.92 1.2 2.43a9.95 9.95 0 0 1-3.17 3.13l-2.3-1.13-.77 2.4a9.94 9.94 0 0 1-4.5-.02l-.73-2.26zM12 14.5a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5zm0 1.5a4 4 0 1 1 0-8 4 4 0 0 1 0 8z'
      ></path>
    </svg>
  );
};

export default ConfigurationIcon;
