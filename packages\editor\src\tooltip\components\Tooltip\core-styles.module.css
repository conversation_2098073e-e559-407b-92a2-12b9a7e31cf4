.tooltip {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  opacity: 0;
  will-change: opacity;
}

.fixed {
  position: fixed;
}

.arrow {
  position: absolute;
  background: inherit;
}

.noArrow {
  display: none;
}

.clickable {
  pointer-events: auto;
}

.show {
  opacity: 0.9;
  transition: opacity 0.15s ease-out;
}

.closing {
  opacity: 0;
  transition: opacity 0.15s ease-in;
}

/** end - core styles **/
