{"name": "<PERSON><PERSON>-editor", "private": false, "version": "1.0.71", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "homepage": "https://www.canvaclone.com", "repository": {"type": "git", "url": "https://github.com/kenvinlu/canva-editor"}, "license": "MIT License with No Resale Clause", "keywords": ["canva clone", "canva editor", "<PERSON><PERSON><PERSON><PERSON>", "canva.js", "react canvajs", "graphic design tool", "online design software", "react design app", "canva alternative", "responsive design", "drag-and-drop design", "collaboration in design", "user-friendly design tool", "seo-friendly design", "social media graphics tool", "javascript design tool", "canva-like editor", "canva-inspired design", "canva-style graphics", "canva alternative for react"], "main": "./dist/canva-editor.umd.js", "module": "./dist/canva-editor.es.js", "types": "./dist/packages/editor/src/components/editor/index.d.ts", "exports": {".": {"import": "./dist/canva-editor.es.js", "require": "./dist/canva-editor.umd.js"}, "./index.module.css": {"import": "./dist/index.module.css", "require": "./dist/index.module.css"}}, "files": ["dist"], "dependencies": {"@emotion/css": "^11.11.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@floating-ui/dom": "^1.5.3", "axios": "^1.5.1", "classnames": "^2.3.2", "framer-motion": "^10.16.9", "immer": "^10.0.3", "jspdf": "^2.5.1", "lodash": "^4.17.21", "modern-screenshot": "^4.4.38", "prop-types": "^15.8.1", "prosemirror-commands": "^1.5.2", "prosemirror-gapcursor": "^1.3.2", "prosemirror-history": "^1.3.2", "prosemirror-inputrules": "^1.2.1", "prosemirror-state": "^1.4.3", "react-device-detect": "^2.2.3", "react-singleton-hook": "^4.0.1", "styled-components": "^6.1.8", "svg-path-round-corners": "^0.1.5", "w3c-keyname": "^2.2.8"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/react": "^18.2.63", "@types/react-dom": "^18.2.20"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "bugs": {"url": "https://github.com/kenvinlu/canva-editor/issues"}}