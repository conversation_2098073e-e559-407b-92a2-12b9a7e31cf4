html,
body {
  margin: 0;
  font-family: "Canva Sans", sans-serif;
  color: #5e6278;
  font-size: 14px;
}

html,
body {
  overflow-wrap: break-word;
  -webkit-hyphens: none;
  hyphens: none;
  word-break: break-word;
  margin: 0;
  display: flex;
  flex-direction: column;
  height: unset;
  overscroll-behavior: none;
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

button {
  background-color: transparent;
  background-image: none;
}

fieldset {
  margin: 0;
  padding: 0;
}

ol,
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

body {
  font-family: inherit;
  line-height: inherit;
}

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: currentColor; /* 2 */
  outline: none;
}

hr {
  border-top-width: 1px;
}

img {
  border-style: solid;
}

textarea {
  resize: vertical;
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  color: #a1a1aa;
}

button,
[role="button"] {
  cursor: pointer;
}

table {
  border-collapse: collapse;
}

a {
  color: inherit;
  text-decoration: inherit;
}

button,
input,
optgroup,
select,
textarea {
  padding: 0;
  line-height: inherit;
  color: inherit;
  font-family: "Canva Sans", sans-serif;
}

pre,
code,
kbd,
samp {
  font-family: Arial, sans-serif;
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  vertical-align: middle;
}

img,
video {
  max-width: 100%;
  height: auto;
}
.slide-indicator {
  transition: opacity 0.3s ease;
}

input[type="text"],
input[type="tel"] {
  background-color: white;
}
