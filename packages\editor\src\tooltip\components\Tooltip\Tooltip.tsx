import React, { useEffect, useState, useRef, useCallback } from 'react';
import { autoUpdate } from '@floating-ui/dom';
import debounce from '../../utils/debounce';
import { useTooltip } from '../TooltipProvider';
import useIsomorphicLayoutEffect from '../../utils/use-isomorphic-layout-effect';
import { getScrollParent } from '../../utils/get-scroll-parent';
import { computeTooltipPosition } from '../../utils/compute-positions';
import coreStyles from './core-styles.module.css';
import styles from './styles.module.css';
import classNames from 'classnames';
import type {
  AnchorCloseEvents,
  AnchorOpenEvents,
  GlobalCloseEvents,
  IPosition,
  ITooltip,
  PlacesType,
} from './TooltipTypes';

const Tooltip = ({
  // props
  id,
  className,
  classNameArrow,
  variant = 'dark',
  anchorId,
  anchorSelect,
  place = 'top',
  offset = 10,
  events = ['hover'],
  openOnClick = false,
  positionStrategy = 'absolute',
  middlewares,
  wrapper: WrapperElement,
  delayShow = 0,
  delayHide = 0,
  float = false,
  hidden = false,
  noArrow = false,
  clickable = false,
  closeOnEsc = false,
  closeOnScroll = false,
  closeOnResize = false,
  openEvents,
  closeEvents,
  globalCloseEvents,
  style: externalStyles,
  position,
  afterShow,
  afterHide,
  // props handled by controller
  content,
  contentWrapperRef,
  isOpen,
  setIsOpen,
  activeAnchor,
  setActiveAnchor,
  border,
  opacity,
  arrowColor,
}: ITooltip) => {
  const tooltipRef = useRef<HTMLElement>(null);
  const tooltipArrowRef = useRef<HTMLElement>(null);
  const tooltipShowDelayTimerRef = useRef<any>(null);
  const tooltipHideDelayTimerRef = useRef<any>(null);
  const [actualPlacement, setActualPlacement] = useState(place);
  const [inlineStyles, setInlineStyles] = useState({});
  const [inlineArrowStyles, setInlineArrowStyles] = useState({});
  const [show, setShow] = useState(false);
  const [rendered, setRendered] = useState(false);
  const wasShowing = useRef(false);
  const lastFloatPosition = useRef<IPosition | null>(null);
  /**
   * @todo Remove this in a future version (provider/wrapper method is deprecated)
   */
  const { anchorRefs, setActiveAnchor: setProviderActiveAnchor } =
    useTooltip(id);
  const hoveringTooltip = useRef(false);
  const [anchorsBySelect, setAnchorsBySelect] = useState<HTMLElement[]>([]);
  const mounted = useRef(false);

  /**
   * @todo Update when deprecated stuff gets removed.
   */
  const shouldOpenOnClick = openOnClick || events.includes('click');
  const hasClickEvent =
    shouldOpenOnClick ||
    openEvents?.click ||
    openEvents?.dblclick ||
    openEvents?.mousedown;
  const actualOpenEvents: AnchorOpenEvents = openEvents
    ? { ...openEvents }
    : {
        mouseenter: true,
        focus: true,
        click: false,
        dblclick: false,
        mousedown: false,
      };
  if (!openEvents && shouldOpenOnClick) {
    Object.assign(actualOpenEvents, {
      mouseenter: false,
      focus: false,
      click: true,
    });
  }
  const actualCloseEvents: AnchorCloseEvents = closeEvents
    ? { ...closeEvents }
    : {
        mouseleave: true,
        blur: true,
        click: false,
      };
  if (!closeEvents && shouldOpenOnClick) {
    Object.assign(actualCloseEvents, {
      mouseleave: false,
      blur: false,
    });
  }
  const actualGlobalCloseEvents: GlobalCloseEvents = globalCloseEvents
    ? { ...globalCloseEvents }
    : {
        escape: closeOnEsc || false,
        scroll: closeOnScroll || false,
        resize: closeOnResize || false,
        clickOutsideAnchor: hasClickEvent || false,
      };

  /**
   * useLayoutEffect runs before useEffect,
   * but should be used carefully because of caveats
   * https://beta.reactjs.org/reference/react/useLayoutEffect#caveats
   */
  useIsomorphicLayoutEffect(() => {
    mounted.current = true;
    return () => {
      mounted.current = false;
    };
  }, []);

  const handleShow = (value: boolean) => {
    if (!mounted.current) {
      return;
    }
    if (value) {
      setRendered(true);
    }
    /**
     * wait for the component to render and calculate position
     * before actually showing
     */
    setTimeout(() => {
      if (!mounted.current) {
        return;
      }
      setIsOpen?.(value);
      if (isOpen === undefined) {
        setShow(value);
      }
    }, 10);
  };

  /**
   * this replicates the effect from `handleShow()`
   * when `isOpen` is changed from outside
   */
  useEffect(() => {
    if (isOpen === undefined) {
      return () => null;
    }
    if (isOpen) {
      setRendered(true);
    }
    const timeout = setTimeout(() => {
      setShow(isOpen);
    }, 10);
    return () => {
      clearTimeout(timeout);
    };
  }, [isOpen]);

  useEffect(() => {
    if (show === wasShowing.current) {
      return;
    }
    wasShowing.current = show;
    if (show) {
      afterShow?.();
    } else {
      afterHide?.();
    }
  }, [show]);

  const handleShowTooltipDelayed = () => {
    if (tooltipShowDelayTimerRef.current) {
      clearTimeout(tooltipShowDelayTimerRef.current);
    }

    tooltipShowDelayTimerRef.current = setTimeout(() => {
      handleShow(true);
    }, delayShow);
  };

  const handleHideTooltipDelayed = (delay = delayHide) => {
    if (tooltipHideDelayTimerRef.current) {
      clearTimeout(tooltipHideDelayTimerRef.current);
    }

    tooltipHideDelayTimerRef.current = setTimeout(() => {
      if (hoveringTooltip.current) {
        return;
      }
      handleShow(false);
    }, delay);
  };

  const handleShowTooltip = (event?: Event) => {
    if (!event) {
      return;
    }
    const target = (event.currentTarget ?? event.target) as HTMLElement | null;
    if (!target?.isConnected) {
      /**
       * this happens when the target is removed from the DOM
       * at the same time the tooltip gets triggered
       */
      setActiveAnchor(null);
      setProviderActiveAnchor({ current: null });
      return;
    }
    if (delayShow) {
      handleShowTooltipDelayed();
    } else {
      handleShow(true);
    }
    setActiveAnchor(target);
    setProviderActiveAnchor({ current: target });

    if (tooltipHideDelayTimerRef.current) {
      clearTimeout(tooltipHideDelayTimerRef.current);
    }
  };

  const handleHideTooltip = () => {
    if (clickable) {
      // allow time for the mouse to reach the tooltip, in case there's a gap
      handleHideTooltipDelayed(delayHide || 100);
    } else if (delayHide) {
      handleHideTooltipDelayed();
    } else {
      handleShow(false);
    }

    if (tooltipShowDelayTimerRef.current) {
      clearTimeout(tooltipShowDelayTimerRef.current);
    }
  };

  const handleTooltipPosition = ({ x, y }: IPosition) => {
    const virtualElement = {
      getBoundingClientRect() {
        return {
          x,
          y,
          width: 0,
          height: 0,
          top: y,
          left: x,
          right: x,
          bottom: y,
        };
      },
    } as Element;
    computeTooltipPosition({
      place,
      offset,
      elementReference: virtualElement,
      tooltipReference: tooltipRef.current,
      tooltipArrowReference: tooltipArrowRef.current,
      strategy: positionStrategy,
      middlewares,
      border,
    }).then((computedStylesData) => {
      if (Object.keys(computedStylesData.tooltipStyles).length) {
        setInlineStyles(computedStylesData.tooltipStyles);
      }
      if (Object.keys(computedStylesData.tooltipArrowStyles).length) {
        setInlineArrowStyles(computedStylesData.tooltipArrowStyles);
      }
      setActualPlacement(computedStylesData.place as PlacesType);
    });
  };

  const handleMouseMove = (event?: Event) => {
    if (!event) {
      return;
    }
    const mouseEvent = event as MouseEvent;
    const mousePosition = {
      x: mouseEvent.clientX,
      y: mouseEvent.clientY,
    };
    handleTooltipPosition(mousePosition);
    lastFloatPosition.current = mousePosition;
  };

  const handleClickOutsideAnchors = (event: MouseEvent) => {
    const anchorById = document.querySelector<HTMLElement>(
      `[id='${anchorId}']`
    );
    const anchors = [anchorById, ...anchorsBySelect];
    if (
      anchors.some((anchor) => anchor?.contains(event.target as HTMLElement))
    ) {
      return;
    }
    if (tooltipRef.current?.contains(event.target as HTMLElement)) {
      return;
    }
    handleShow(false);
    if (tooltipShowDelayTimerRef.current) {
      clearTimeout(tooltipShowDelayTimerRef.current);
    }
  };

  // debounce handler to prevent call twice when
  // mouse enter and focus events being triggered toggether
  const debouncedHandleShowTooltip = debounce(handleShowTooltip, 50, true);
  const debouncedHandleHideTooltip = debounce(handleHideTooltip, 50, true);
  const updateTooltipPosition = useCallback(() => {
    if (position) {
      // if `position` is set, override regular and `float` positioning
      handleTooltipPosition(position);
      return;
    }

    if (float) {
      if (lastFloatPosition.current) {
        /*
          Without this, changes to `content`, `place`, `offset`, ..., will only
          trigger a position calculation after a `mousemove` event.

          To see why this matters, comment this line, run `yarn dev` and click the
          "Hover me!" anchor.
        */
        handleTooltipPosition(lastFloatPosition.current);
      }
      // if `float` is set, override regular positioning
      return;
    }

    if (!activeAnchor?.isConnected) {
      return;
    }

    computeTooltipPosition({
      place,
      offset,
      elementReference: activeAnchor,
      tooltipReference: tooltipRef.current,
      tooltipArrowReference: tooltipArrowRef.current,
      strategy: positionStrategy,
      middlewares,
      border,
    }).then((computedStylesData) => {
      if (!mounted.current) {
        // invalidate computed positions after remount
        return;
      }
      if (Object.keys(computedStylesData.tooltipStyles).length) {
        setInlineStyles(computedStylesData.tooltipStyles);
      }
      if (Object.keys(computedStylesData.tooltipArrowStyles).length) {
        setInlineArrowStyles(computedStylesData.tooltipArrowStyles);
      }
      setActualPlacement(computedStylesData.place as PlacesType);
    });
  }, [
    show,
    activeAnchor,
    content,
    externalStyles,
    place,
    offset,
    positionStrategy,
    position,
    float,
  ]);

  useEffect(() => {
    const elementRefs = new Set(anchorRefs);

    anchorsBySelect.forEach((anchor) => {
      elementRefs.add({ current: anchor });
    });

    const anchorById = document.querySelector<HTMLElement>(
      `[id='${anchorId}']`
    );
    if (anchorById) {
      elementRefs.add({ current: anchorById });
    }

    const handleScrollResize = () => {
      handleShow(false);
    };

    const anchorScrollParent = getScrollParent(activeAnchor);
    const tooltipScrollParent = getScrollParent(tooltipRef.current);

    if (actualGlobalCloseEvents.scroll) {
      window.addEventListener('scroll', handleScrollResize);
      anchorScrollParent?.addEventListener('scroll', handleScrollResize);
      tooltipScrollParent?.addEventListener('scroll', handleScrollResize);
    }
    let updateTooltipCleanup: null | (() => void) = null;
    if (actualGlobalCloseEvents.resize) {
      window.addEventListener('resize', handleScrollResize);
    } else if (activeAnchor && tooltipRef.current) {
      updateTooltipCleanup = autoUpdate(
        activeAnchor as HTMLElement,
        tooltipRef.current as HTMLElement,
        updateTooltipPosition,
        {
          ancestorResize: true,
          elementResize: true,
          layoutShift: true,
        }
      );
    }

    const handleEsc = (event: KeyboardEvent) => {
      if (event.key !== 'Escape') {
        return;
      }
      handleShow(false);
    };
    if (actualGlobalCloseEvents.escape) {
      window.addEventListener('keydown', handleEsc);
    }

    if (actualGlobalCloseEvents.clickOutsideAnchor) {
      window.addEventListener('click', handleClickOutsideAnchors);
    }

    const enabledEvents: {
      event: string;
      listener: (event?: Event) => void;
    }[] = [];

    const handleClickOpenTooltipAnchor = (event?: Event) => {
      if (show) {
        return;
      }
      handleShowTooltip(event);
    };
    const handleClickCloseTooltipAnchor = () => {
      if (!show) {
        return;
      }
      handleHideTooltip();
    };

    const regularEvents = ['mouseenter', 'mouseleave', 'focus', 'blur'];
    const clickEvents = ['click', 'dblclick', 'mousedown', 'mouseup'];

    Object.entries(actualOpenEvents).forEach(([event, enabled]) => {
      if (!enabled) {
        return;
      }
      if (regularEvents.includes(event)) {
        enabledEvents.push({ event, listener: debouncedHandleShowTooltip });
      } else if (clickEvents.includes(event)) {
        enabledEvents.push({ event, listener: handleClickOpenTooltipAnchor });
      } else {
        // never happens
      }
    });

    Object.entries(actualCloseEvents).forEach(([event, enabled]) => {
      if (!enabled) {
        return;
      }
      if (regularEvents.includes(event)) {
        enabledEvents.push({ event, listener: debouncedHandleHideTooltip });
      } else if (clickEvents.includes(event)) {
        enabledEvents.push({ event, listener: handleClickCloseTooltipAnchor });
      } else {
        // never happens
      }
    });

    if (float) {
      enabledEvents.push({
        event: 'mousemove',
        listener: handleMouseMove,
      });
    }

    const handleMouseEnterTooltip = () => {
      hoveringTooltip.current = true;
    };
    const handleMouseLeaveTooltip = () => {
      hoveringTooltip.current = false;
      handleHideTooltip();
    };

    if (clickable && !hasClickEvent) {
      // used to keep the tooltip open when hovering content.
      // not needed if using click events.
      tooltipRef.current?.addEventListener(
        'mouseenter',
        handleMouseEnterTooltip
      );
      tooltipRef.current?.addEventListener(
        'mouseleave',
        handleMouseLeaveTooltip
      );
    }

    enabledEvents.forEach(({ event, listener }) => {
      elementRefs.forEach((ref) => {
        ref.current?.addEventListener(event, listener);
      });
    });

    return () => {
      if (actualGlobalCloseEvents.scroll) {
        window.removeEventListener('scroll', handleScrollResize);
        anchorScrollParent?.removeEventListener('scroll', handleScrollResize);
        tooltipScrollParent?.removeEventListener('scroll', handleScrollResize);
      }
      if (actualGlobalCloseEvents.resize) {
        window.removeEventListener('resize', handleScrollResize);
      } else {
        updateTooltipCleanup?.();
      }
      if (actualGlobalCloseEvents.clickOutsideAnchor) {
        window.removeEventListener('click', handleClickOutsideAnchors);
      }
      if (actualGlobalCloseEvents.escape) {
        window.removeEventListener('keydown', handleEsc);
      }
      if (clickable && !hasClickEvent) {
        tooltipRef.current?.removeEventListener(
          'mouseenter',
          handleMouseEnterTooltip
        );
        tooltipRef.current?.removeEventListener(
          'mouseleave',
          handleMouseLeaveTooltip
        );
      }
      enabledEvents.forEach(({ event, listener }) => {
        elementRefs.forEach((ref) => {
          ref.current?.removeEventListener(event, listener);
        });
      });
    };
    /**
     * rendered is also a dependency to ensure anchor observers are re-registered
     * since `tooltipRef` becomes stale after removing/adding the tooltip to the DOM
     */
  }, [
    activeAnchor,
    updateTooltipPosition,
    rendered,
    anchorRefs,
    anchorsBySelect,
    // the effect uses the `actual*Events` objects, but this should work
    openEvents,
    closeEvents,
    globalCloseEvents,
    shouldOpenOnClick,
  ]);

  useEffect(() => {
    let selector = anchorSelect ?? '';
    if (!selector && id) {
      selector = `[data-tooltip-id='${id}']`;
    }
    const documentObserverCallback: MutationCallback = (mutationList) => {
      const newAnchors: HTMLElement[] = [];
      const removedAnchors: HTMLElement[] = [];
      mutationList.forEach((mutation) => {
        if (
          mutation.type === 'attributes' &&
          mutation.attributeName === 'data-tooltip-id'
        ) {
          const newId = (mutation.target as HTMLElement).getAttribute(
            'data-tooltip-id'
          );
          if (newId === id) {
            newAnchors.push(mutation.target as HTMLElement);
          }
        }
        if (mutation.type !== 'childList') {
          return;
        }
        if (activeAnchor) {
          const elements = [...mutation.removedNodes].filter(
            (node) => node.nodeType === 1
          );
          if (selector) {
            try {
              removedAnchors.push(
                // the element itself is an anchor
                ...(elements.filter((element) =>
                  (element as HTMLElement).matches(selector)
                ) as HTMLElement[])
              );
              removedAnchors.push(
                // the element has children which are anchors
                ...elements.flatMap(
                  (element) =>
                    [
                      ...(element as HTMLElement).querySelectorAll(selector),
                    ] as HTMLElement[]
                )
              );
            } catch {
              /**
               * invalid CSS selector.
               * already warned on tooltip controller
               */
            }
          }
          elements.some((node) => {
            if (node?.contains?.(activeAnchor)) {
              setRendered(false);
              handleShow(false);
              setActiveAnchor(null);
              if (tooltipShowDelayTimerRef.current) {
                clearTimeout(tooltipShowDelayTimerRef.current);
              }
              if (tooltipHideDelayTimerRef.current) {
                clearTimeout(tooltipHideDelayTimerRef.current);
              }
              return true;
            }
            return false;
          });
        }
        if (!selector) {
          return;
        }
        try {
          const elements = [...mutation.addedNodes].filter(
            (node) => node.nodeType === 1
          );
          newAnchors.push(
            // the element itself is an anchor
            ...(elements.filter((element) =>
              (element as HTMLElement).matches(selector)
            ) as HTMLElement[])
          );
          newAnchors.push(
            // the element has children which are anchors
            ...elements.flatMap(
              (element) =>
                [
                  ...(element as HTMLElement).querySelectorAll(selector),
                ] as HTMLElement[]
            )
          );
        } catch {
          /**
           * invalid CSS selector.
           * already warned on tooltip controller
           */
        }
      });
      if (newAnchors.length || removedAnchors.length) {
        setAnchorsBySelect((anchors) => [
          ...anchors.filter((anchor) => !removedAnchors.includes(anchor)),
          ...newAnchors,
        ]);
      }
    };
    const documentObserver = new MutationObserver(documentObserverCallback);
    // watch for anchor being removed from the DOM
    documentObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['data-tooltip-id'],
    });
    return () => {
      documentObserver.disconnect();
    };
  }, [id, anchorSelect, activeAnchor]);

  useEffect(() => {
    updateTooltipPosition();
  }, [updateTooltipPosition]);

  useEffect(() => {
    if (!contentWrapperRef?.current) {
      return () => null;
    }
    const contentObserver = new ResizeObserver(() => {
      updateTooltipPosition();
    });
    contentObserver.observe(contentWrapperRef.current);
    return () => {
      contentObserver.disconnect();
    };
  }, [content, contentWrapperRef?.current]);

  useEffect(() => {
    const anchorById = document.querySelector<HTMLElement>(
      `[id='${anchorId}']`
    );
    const anchors = [...anchorsBySelect, anchorById];
    if (!activeAnchor || !anchors.includes(activeAnchor)) {
      /**
       * if there is no active anchor,
       * or if the current active anchor is not amongst the allowed ones,
       * reset it
       */
      setActiveAnchor(anchorsBySelect[0] ?? anchorById);
    }
  }, [anchorId, anchorsBySelect, activeAnchor]);

  useEffect(() => {
    return () => {
      if (tooltipShowDelayTimerRef.current) {
        clearTimeout(tooltipShowDelayTimerRef.current);
      }
      if (tooltipHideDelayTimerRef.current) {
        clearTimeout(tooltipHideDelayTimerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    let selector = anchorSelect;
    if (!selector && id) {
      selector = `[data-tooltip-id='${id}']`;
    }
    if (!selector) {
      return;
    }
    try {
      const anchors = Array.from(
        document.querySelectorAll<HTMLElement>(selector)
      );
      setAnchorsBySelect(anchors);
    } catch {
      // warning was already issued in the controller
      setAnchorsBySelect([]);
    }
  }, [id, anchorSelect]);

  const canShow =
    !hidden && content && show && Object.keys(inlineStyles).length > 0;

  return rendered ? (
    <WrapperElement
      id={id}
      role='tooltip'
      className={classNames(
        'react-tooltip',
        coreStyles['tooltip'],
        styles['tooltip'],
        styles[variant],
        className,
        `react-tooltip__place-${actualPlacement}`,
        coreStyles[canShow ? 'show' : 'closing'],
        canShow ? 'react-tooltip__show' : 'react-tooltip__closing',
        positionStrategy === 'fixed' && coreStyles['fixed'],
        clickable && coreStyles['clickable']
      )}
      onTransitionEnd={(event: TransitionEvent) => {
        /**
         * @warning if `--rt-transition-closing-delay` is set to 0,
         * the tooltip will be stuck (but not visible) on the DOM
         */
        if (show || event.propertyName !== 'opacity') {
          return;
        }
        setRendered(false);
      }}
      style={{
        ...externalStyles,
        ...inlineStyles,
        opacity: opacity !== undefined && canShow ? opacity : undefined,
      }}
      ref={tooltipRef}
    >
      {content}
      <WrapperElement
        className={classNames(
          'react-tooltip-arrow',
          coreStyles['arrow'],
          styles['arrow'],
          classNameArrow,
          noArrow && coreStyles['noArrow']
        )}
        style={{
          ...inlineArrowStyles,
          background: arrowColor
            ? `linear-gradient(to right bottom, transparent 50%, ${arrowColor} 50%)`
            : undefined,
        }}
        ref={tooltipArrowRef}
      />
    </WrapperElement>
  ) : null;
};

export default Tooltip;
