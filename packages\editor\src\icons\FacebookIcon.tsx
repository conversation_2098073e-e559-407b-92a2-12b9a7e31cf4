import { IconProps } from 'canva-editor/types';
import React from 'react';

const FacebookIcon: React.FC<IconProps> = () => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      width='24px'
      height='24px'
      viewBox='0 0 24 24'
      version='1.1'
    >
      <g id='surface1'>
        <path
        fill='#3f51b5'
          d='M 12 2 C 6.476562 2 2 6.476562 2 12 C 2 17.523438 6.476562 22 12 22 C 17.523438 22 22 17.523438 22 12 C 22 6.476562 17.523438 2 12 2 Z M 12 2 '
        />
        <path
        fill='#fff'
          d='M 14.683594 12 L 13 12 L 13 18 L 10.5 18 L 10.5 12 L 9 12 L 9 10 L 10.5 10 L 10.5 8.796875 C 10.5 7.039062 11.230469 6 13.296875 6 L 15 6 L 15 8 L 13.855469 8 C 13.050781 8 13 8.300781 13 8.863281 L 13 10 L 15 10 Z M 14.683594 12 '
        />
      </g>
    </svg>
  );
};

export default FacebookIcon;
